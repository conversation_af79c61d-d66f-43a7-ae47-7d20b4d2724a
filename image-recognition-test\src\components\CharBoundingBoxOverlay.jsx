import React from 'react';
import { Switch, Space, Typography } from 'antd';
import { EyeOutlined, EyeInvisibleOutlined } from '@ant-design/icons';
import { calculateCharBounds } from '../utils/charPosAnalyzer';

const { Text } = Typography;

/**
 * 字符边界框覆盖层组件
 * 用于在渲染结果上显示TextIn API识别的字符边界框
 */
const CharBoundingBoxOverlay = ({
  textElements = [],
  scale = 1,
  containerWidth = 800,
  containerHeight = 600,
  canvasWidthMm = 40,
  canvasHeightMm = 30,
  originalImageWidthPx = 0,
  originalImageHeightPx = 0,
  visible = false,
  onVisibilityChange,
  showSettingsPanel = true,
  showOverlay = true
}) => {
  // 固定设置 - 不可调整
  const settings = {
    opacity: 0.8,           // 边框透明度
    strokeWidth: 2,         // 边框宽度
    strokeStyle: 'solid',   // 边框样式
    fillOpacity: 0.2,       // 填充透明度
    colorMode: 'single',    // 单一颜色模式
    singleColor: '#ff4d4f', // 红色边框
    showLabels: false,      // 不显示字符标签
    labelSize: 10
  };

  // 预定义的彩虹色彩
  const rainbowColors = [
    '#ff4d4f', '#ff7a45', '#ffa940', '#ffec3d', 
    '#bae637', '#52c41a', '#13c2c2', '#1890ff',
    '#2f54eb', '#722ed1', '#eb2f96', '#f759ab'
  ];

  // 获取字符颜色
  const getCharColor = (charIndex, elementIndex) => {
    switch (settings.colorMode) {
      case 'rainbow':
        return rainbowColors[charIndex % rainbowColors.length];
      case 'byElement':
        return rainbowColors[elementIndex % rainbowColors.length];
      case 'single':
      default:
        return settings.singleColor;
    }
  };

  // 获取原始图像尺寸
  const getOriginalImageSize = () => {
    // 优先使用传递的准确原始图像尺寸
    if (originalImageWidthPx > 0 && originalImageHeightPx > 0) {
      return {
        width: originalImageWidthPx,
        height: originalImageHeightPx
      };
    }

    // 如果没有准确尺寸，则从char_pos推断（备用方案）
    let maxX = 0, maxY = 0;

    textElements.forEach(element => {
      if (element.char_pos && Array.isArray(element.char_pos)) {
        element.char_pos.forEach(charPos => {
          if (charPos && charPos.length === 8) {
            const xs = [charPos[0], charPos[2], charPos[4], charPos[6]];
            const ys = [charPos[1], charPos[3], charPos[5], charPos[7]];
            maxX = Math.max(maxX, ...xs);
            maxY = Math.max(maxY, ...ys);
          }
        });
      }
    });

    // 添加一些边距，因为字符可能不会到达图像边缘
    return {
      width: maxX * 1.1,
      height: maxY * 1.1
    };
  };

  // 渲染单个字符边界框
  const renderCharBounds = (charPos, charIndex, elementIndex, char, textElement) => {
    if (!charPos || charPos.length !== 8) {
      console.log(`字符 ${charIndex} 的charPos数据无效:`, charPos);
      return null;
    }

    const bounds = calculateCharBounds(charPos);

    // 检查边界框是否有效
    if (!bounds || bounds.width <= 0 || bounds.height <= 0) {
      console.log(`字符 "${char}" (${charIndex}) 边界框无效:`, bounds);
      return null;
    }

    // 获取原始图像尺寸
    const originalImageSize = getOriginalImageSize();

    // 正确的坐标转换：
    // 1. char_pos (原始图像px) → XPrinter坐标 (mm)
    // 2. XPrinter坐标 (mm) → 显示坐标 (px)

    // 步骤1: 原始图像像素 → XPrinter毫米
    const xMm = (bounds.x / originalImageSize.width) * canvasWidthMm;
    const yMm = (bounds.y / originalImageSize.height) * canvasHeightMm;
    const widthMm = (bounds.width / originalImageSize.width) * canvasWidthMm;
    const heightMm = (bounds.height / originalImageSize.height) * canvasHeightMm;

    // 步骤2: XPrinter毫米 → 显示像素
    const scaledBounds = {
      x: xMm * scale,
      y: yMm * scale,
      width: widthMm * scale,
      height: heightMm * scale
    };

    // 检查坐标是否在可见范围内
    const isVisible = scaledBounds.x >= 0 && scaledBounds.y >= 0 &&
                     scaledBounds.x < containerWidth && scaledBounds.y < containerHeight;

    // 只在第一个字符时输出调试信息
    if (charIndex === 0 && elementIndex === 0) {
      console.log(`🔍 字符边界框坐标转换详情 - 字符: "${char}"`);
      console.log(`📐 传入的原始图像尺寸: ${originalImageWidthPx}×${originalImageHeightPx}px`);
      console.log(`📏 计算的原始图像尺寸: ${originalImageSize.width.toFixed(1)}×${originalImageSize.height.toFixed(1)}px`);
      console.log(`📋 画布尺寸: ${canvasWidthMm}×${canvasHeightMm}mm`);
      console.log(`📍 原始坐标: (${bounds.x}, ${bounds.y}) ${bounds.width}×${bounds.height}px`);
      console.log(`🎯 XPrinter坐标: (${xMm.toFixed(2)}, ${yMm.toFixed(2)}) ${widthMm.toFixed(2)}×${heightMm.toFixed(2)}mm`);
      console.log(`🖥️ 显示坐标: (${scaledBounds.x.toFixed(1)}, ${scaledBounds.y.toFixed(1)}) ${scaledBounds.width.toFixed(1)}×${scaledBounds.height.toFixed(1)}px`);
      console.log(`📊 缩放比例: ${scale.toFixed(2)}`);
      console.log(`👁️ 完全可见: ${isVisible}`);
      console.log('---');
    }

    const color = getCharColor(charIndex, elementIndex);
    const uniqueKey = `char-${elementIndex}-${charIndex}`;

    return (
      <g key={uniqueKey}>
        {/* 边界框矩形 */}
        <rect
          x={Math.max(0, Math.min(scaledBounds.x, containerWidth - scaledBounds.width))}
          y={Math.max(0, Math.min(scaledBounds.y, containerHeight - scaledBounds.height))}
          width={scaledBounds.width}
          height={scaledBounds.height}
          fill={color}
          fillOpacity={settings.fillOpacity}
          stroke={color}
          strokeWidth={settings.strokeWidth}
          strokeOpacity={settings.opacity}
          strokeDasharray={settings.strokeStyle === 'dashed' ? '5,5' : 'none'}
        />
        
        {/* 字符标签 */}
        {settings.showLabels && (
          <text
            x={scaledBounds.x + scaledBounds.width / 2}
            y={scaledBounds.y + scaledBounds.height / 2}
            textAnchor="middle"
            dominantBaseline="middle"
            fontSize={settings.labelSize}
            fill={color}
            fillOpacity={Math.min(1, settings.opacity + 0.3)}
            fontWeight="bold"
            style={{ pointerEvents: 'none' }}
          >
            {char}
          </text>
        )}
        
        {/* 字符索引 */}
        {settings.showLabels && (
          <text
            x={scaledBounds.x + 2}
            y={scaledBounds.y + settings.labelSize + 2}
            fontSize={Math.max(8, settings.labelSize - 2)}
            fill={color}
            fillOpacity={settings.opacity}
            style={{ pointerEvents: 'none' }}
          >
            {charIndex}
          </text>
        )}
      </g>
    );
  };

  // 渲染所有字符边界框
  const renderAllCharBounds = () => {
    const bounds = [];

    textElements.forEach((element, elementIndex) => {
      // 检查元素是否有char_pos数据
      if (!element.char_pos || !Array.isArray(element.char_pos)) {
        return;
      }

      const text = element.content || '';

      element.char_pos.forEach((charPos, charIndex) => {
        if (charIndex < text.length) {
          const char = text[charIndex];
          const charBound = renderCharBounds(charPos, charIndex, elementIndex, char, element);
          if (charBound) {
            bounds.push(charBound);
          }
        }
      });
    });

    return bounds;
  };



  // 切换显示状态
  const toggleVisibility = () => {
    const newVisibility = !visible;
    if (onVisibilityChange) {
      onVisibilityChange(newVisibility);
    }
  };



  // 只显示SVG覆盖层
  if (!showSettingsPanel && showOverlay) {
    console.log('渲染SVG覆盖层 - 调试信息:', {
      showBounds: visible,
      textElementsCount: textElements?.length || 0,
      containerSize: { width: containerWidth, height: containerHeight },
      hasCharPosData: textElements?.some(el => el.char_pos?.length > 0)
    });

    return (
      <svg
        width={containerWidth}
        height={containerHeight}
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          pointerEvents: 'none',
          zIndex: 9999
        }}
      >




        {/* 字符边界框 */}
        {visible && renderAllCharBounds()}

        {/* 如果没有字符边界框，显示调试信息 */}
        {settings.showBounds && (!textElements || textElements.length === 0) && (
          <text
            x="50"
            y="200"
            fontSize="14"
            fill="orange"
            fontWeight="bold"
          >
            没有文本元素数据
          </text>
        )}

        {/* 显示文本元素数量 */}
        <text
          x="50"
          y="250"
          fontSize="12"
          fill="blue"
          fontWeight="bold"
        >
          文本元素: {textElements?.length || 0}
        </text>
      </svg>
    );
  }

  // 只显示设置面板
  if (showSettingsPanel && !showOverlay) {
    return (
      <Space>
        <Switch
          checked={visible}
          onChange={toggleVisibility}
          checkedChildren={<EyeOutlined />}
          unCheckedChildren={<EyeInvisibleOutlined />}
        />
        <Text>字符边框</Text>
      </Space>
    );
  }


  // 只显示SVG覆盖层
  if (!showSettingsPanel && showOverlay) {
    return (
      <div style={{ position: 'absolute', top: 0, left: 0, width: '100%', height: '100%', pointerEvents: 'none' }}>
        {visible && (
          <svg
            width={containerWidth}
            height={containerHeight}
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              pointerEvents: 'none',
              zIndex: 9999
            }}
          >
            {renderAllCharBounds()}
          </svg>
        )}
      </div>
    );
  }

  // 显示完整组件（简化版）- 这个分支不应该被执行
  // 如果到达这里，说明参数配置有问题
  console.warn('CharBoundingBoxOverlay: 意外的参数组合', { showSettingsPanel, showOverlay });
  return (
    <div style={{ position: 'relative', width: '100%' }}>
      {/* 简化的控制面板 */}
      <Space style={{ marginBottom: '16px' }}>
        <Switch
          checked={visible}
          onChange={toggleVisibility}
          checkedChildren={<EyeOutlined />}
          unCheckedChildren={<EyeInvisibleOutlined />}
        />
        <Text>字符边框</Text>
      </Space>

      {/* SVG 覆盖层 */}
      {visible && (
        <div style={{ position: 'relative' }}>
          <svg
            width={containerWidth}
            height={containerHeight}
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              pointerEvents: 'none',
              zIndex: 9999
            }}
          >
            {renderAllCharBounds()}
          </svg>
        </div>
      )}

      {/* 统计信息 */}
      {visible && (
        <div style={{ marginTop: '8px' }}>
          <Text type="secondary">
            显示了 {textElements.reduce((total, element) =>
              total + (element.char_pos?.length || 0), 0
            )} 个字符边界框，
            来自 {textElements.filter(el => el.char_pos?.length > 0).length} 个文本元素
          </Text>
        </div>
      )}
    </div>
  );
};

export default CharBoundingBoxOverlay;
