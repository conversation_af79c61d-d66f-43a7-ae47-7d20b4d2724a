import React, { useState, useEffect } from 'react';
import { Switch, Slider, Select, Space, Typography, Card, Row, Col, ColorPicker } from 'antd';
import { EyeOutlined, EyeInvisibleOutlined, SettingOutlined } from '@ant-design/icons';
import { calculateCharBounds } from '../utils/charPosAnalyzer';

const { Text } = Typography;
const { Option } = Select;

/**
 * 字符边界框覆盖层组件
 * 用于在渲染结果上显示TextIn API识别的字符边界框
 */
const CharBoundingBoxOverlay = ({ 
  textElements = [], 
  scale = 1, 
  containerWidth = 800, 
  containerHeight = 600,
  visible = false,
  onVisibilityChange 
}) => {
  const [settings, setSettings] = useState({
    showBounds: visible,
    opacity: 0.6,
    strokeWidth: 2,
    strokeStyle: 'solid',
    fillOpacity: 0.1,
    colorMode: 'rainbow', // rainbow, single, byElement
    singleColor: '#ff4d4f',
    showLabels: true,
    labelSize: 10
  });

  // 预定义的彩虹色彩
  const rainbowColors = [
    '#ff4d4f', '#ff7a45', '#ffa940', '#ffec3d', 
    '#bae637', '#52c41a', '#13c2c2', '#1890ff',
    '#2f54eb', '#722ed1', '#eb2f96', '#f759ab'
  ];

  // 获取字符颜色
  const getCharColor = (charIndex, elementIndex) => {
    switch (settings.colorMode) {
      case 'rainbow':
        return rainbowColors[charIndex % rainbowColors.length];
      case 'byElement':
        return rainbowColors[elementIndex % rainbowColors.length];
      case 'single':
      default:
        return settings.singleColor;
    }
  };

  // 渲染单个字符边界框
  const renderCharBounds = (charPos, charIndex, elementIndex, char, textElement) => {
    if (!charPos || charPos.length !== 8) return null;

    const bounds = calculateCharBounds(charPos);
    
    // 将原始像素坐标转换为当前显示坐标
    const scaledBounds = {
      x: bounds.x * scale,
      y: bounds.y * scale,
      width: bounds.width * scale,
      height: bounds.height * scale
    };

    const color = getCharColor(charIndex, elementIndex);
    const uniqueKey = `char-${elementIndex}-${charIndex}`;

    return (
      <g key={uniqueKey}>
        {/* 边界框矩形 */}
        <rect
          x={scaledBounds.x}
          y={scaledBounds.y}
          width={scaledBounds.width}
          height={scaledBounds.height}
          fill={color}
          fillOpacity={settings.fillOpacity}
          stroke={color}
          strokeWidth={settings.strokeWidth}
          strokeOpacity={settings.opacity}
          strokeDasharray={settings.strokeStyle === 'dashed' ? '5,5' : 'none'}
        />
        
        {/* 字符标签 */}
        {settings.showLabels && (
          <text
            x={scaledBounds.x + scaledBounds.width / 2}
            y={scaledBounds.y + scaledBounds.height / 2}
            textAnchor="middle"
            dominantBaseline="middle"
            fontSize={settings.labelSize}
            fill={color}
            fillOpacity={settings.opacity + 0.3}
            fontWeight="bold"
            style={{ pointerEvents: 'none' }}
          >
            {char}
          </text>
        )}
        
        {/* 字符索引 */}
        {settings.showLabels && (
          <text
            x={scaledBounds.x + 2}
            y={scaledBounds.y + settings.labelSize + 2}
            fontSize={Math.max(8, settings.labelSize - 2)}
            fill={color}
            fillOpacity={settings.opacity}
            style={{ pointerEvents: 'none' }}
          >
            {charIndex}
          </text>
        )}
      </g>
    );
  };

  // 渲染所有字符边界框
  const renderAllCharBounds = () => {
    const bounds = [];
    
    textElements.forEach((element, elementIndex) => {
      // 检查元素是否有char_pos数据
      if (!element.char_pos || !Array.isArray(element.char_pos)) {
        return;
      }

      const text = element.content || '';
      
      element.char_pos.forEach((charPos, charIndex) => {
        if (charIndex < text.length) {
          const char = text[charIndex];
          const charBound = renderCharBounds(charPos, charIndex, elementIndex, char, element);
          if (charBound) {
            bounds.push(charBound);
          }
        }
      });
    });

    return bounds;
  };

  // 更新设置
  const updateSetting = (key, value) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  // 切换显示状态
  const toggleVisibility = () => {
    const newVisibility = !settings.showBounds;
    updateSetting('showBounds', newVisibility);
    if (onVisibilityChange) {
      onVisibilityChange(newVisibility);
    }
  };

  useEffect(() => {
    updateSetting('showBounds', visible);
  }, [visible]);

  return (
    <div style={{ position: 'relative', width: '100%' }}>
      {/* 控制面板 */}
      <Card 
        size="small" 
        title={
          <Space>
            <SettingOutlined />
            <Text>字符边界框设置</Text>
          </Space>
        }
        style={{ marginBottom: '16px' }}
      >
        <Row gutter={[16, 8]}>
          <Col span={6}>
            <Space>
              <Switch
                checked={settings.showBounds}
                onChange={toggleVisibility}
                checkedChildren={<EyeOutlined />}
                unCheckedChildren={<EyeInvisibleOutlined />}
              />
              <Text>显示边界框</Text>
            </Space>
          </Col>
          
          <Col span={6}>
            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              <Text>透明度</Text>
              <Slider
                min={0.1}
                max={1}
                step={0.1}
                value={settings.opacity}
                onChange={(value) => updateSetting('opacity', value)}
                disabled={!settings.showBounds}
              />
            </Space>
          </Col>
          
          <Col span={6}>
            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              <Text>线条宽度</Text>
              <Slider
                min={1}
                max={5}
                step={1}
                value={settings.strokeWidth}
                onChange={(value) => updateSetting('strokeWidth', value)}
                disabled={!settings.showBounds}
              />
            </Space>
          </Col>
          
          <Col span={6}>
            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              <Text>颜色模式</Text>
              <Select
                value={settings.colorMode}
                onChange={(value) => updateSetting('colorMode', value)}
                disabled={!settings.showBounds}
                style={{ width: '100%' }}
              >
                <Option value="rainbow">彩虹色</Option>
                <Option value="byElement">按元素</Option>
                <Option value="single">单一颜色</Option>
              </Select>
            </Space>
          </Col>
        </Row>
        
        <Row gutter={[16, 8]} style={{ marginTop: '8px' }}>
          <Col span={6}>
            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              <Text>线条样式</Text>
              <Select
                value={settings.strokeStyle}
                onChange={(value) => updateSetting('strokeStyle', value)}
                disabled={!settings.showBounds}
                style={{ width: '100%' }}
              >
                <Option value="solid">实线</Option>
                <Option value="dashed">虚线</Option>
              </Select>
            </Space>
          </Col>
          
          <Col span={6}>
            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              <Text>填充透明度</Text>
              <Slider
                min={0}
                max={0.5}
                step={0.05}
                value={settings.fillOpacity}
                onChange={(value) => updateSetting('fillOpacity', value)}
                disabled={!settings.showBounds}
              />
            </Space>
          </Col>
          
          <Col span={6}>
            <Space>
              <Switch
                checked={settings.showLabels}
                onChange={(checked) => updateSetting('showLabels', checked)}
                disabled={!settings.showBounds}
              />
              <Text>显示标签</Text>
            </Space>
          </Col>
          
          {settings.colorMode === 'single' && (
            <Col span={6}>
              <Space direction="vertical" size="small" style={{ width: '100%' }}>
                <Text>边框颜色</Text>
                <ColorPicker
                  value={settings.singleColor}
                  onChange={(color) => updateSetting('singleColor', color.toHexString())}
                  disabled={!settings.showBounds}
                />
              </Space>
            </Col>
          )}
        </Row>
      </Card>

      {/* SVG 覆盖层 */}
      {settings.showBounds && (
        <div style={{ position: 'relative' }}>
          <svg
            width={containerWidth}
            height={containerHeight}
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              pointerEvents: 'none',
              zIndex: 10
            }}
          >
            {renderAllCharBounds()}
          </svg>
        </div>
      )}

      {/* 统计信息 */}
      {settings.showBounds && (
        <div style={{ marginTop: '8px' }}>
          <Text type="secondary">
            显示了 {textElements.reduce((total, element) => 
              total + (element.char_pos?.length || 0), 0
            )} 个字符边界框，
            来自 {textElements.filter(el => el.char_pos?.length > 0).length} 个文本元素
          </Text>
        </div>
      )}
    </div>
  );
};

export default CharBoundingBoxOverlay;
