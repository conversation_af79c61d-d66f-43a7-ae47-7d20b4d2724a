import React, { useState, useEffect } from 'react';
import { Switch, Slider, Select, Space, Typography, Card, Row, Col, ColorPicker } from 'antd';
import { EyeOutlined, EyeInvisibleOutlined, SettingOutlined } from '@ant-design/icons';
import { calculateCharBounds } from '../utils/charPosAnalyzer';

const { Text } = Typography;
const { Option } = Select;

/**
 * 字符边界框覆盖层组件
 * 用于在渲染结果上显示TextIn API识别的字符边界框
 */
const CharBoundingBoxOverlay = ({
  textElements = [],
  scale = 1,
  containerWidth = 800,
  containerHeight = 600,
  visible = false,
  onVisibilityChange,
  showSettingsPanel = true,
  showOverlay = true
}) => {
  const [settings, setSettings] = useState({
    showBounds: visible,
    opacity: 0.6,
    strokeWidth: 2,
    strokeStyle: 'solid',
    fillOpacity: 0.1,
    colorMode: 'rainbow', // rainbow, single, byElement
    singleColor: '#ff4d4f',
    showLabels: true,
    labelSize: 10
  });

  // 预定义的彩虹色彩
  const rainbowColors = [
    '#ff4d4f', '#ff7a45', '#ffa940', '#ffec3d', 
    '#bae637', '#52c41a', '#13c2c2', '#1890ff',
    '#2f54eb', '#722ed1', '#eb2f96', '#f759ab'
  ];

  // 获取字符颜色
  const getCharColor = (charIndex, elementIndex) => {
    switch (settings.colorMode) {
      case 'rainbow':
        return rainbowColors[charIndex % rainbowColors.length];
      case 'byElement':
        return rainbowColors[elementIndex % rainbowColors.length];
      case 'single':
      default:
        return settings.singleColor;
    }
  };

  // 渲染单个字符边界框
  const renderCharBounds = (charPos, charIndex, elementIndex, char, textElement) => {
    if (!charPos || charPos.length !== 8) {
      console.log(`字符 ${charIndex} 的charPos数据无效:`, charPos);
      return null;
    }

    const bounds = calculateCharBounds(charPos);

    // 检查边界框是否有效
    if (!bounds || bounds.width <= 0 || bounds.height <= 0) {
      console.log(`字符 "${char}" (${charIndex}) 边界框无效:`, bounds);
      return null;
    }

    // 将原始像素坐标转换为当前显示坐标
    // 注意：scale应该是从原始图像像素到显示像素的比例
    const scaledBounds = {
      x: bounds.x * scale,
      y: bounds.y * scale,
      width: bounds.width * scale,
      height: bounds.height * scale
    };

    // 检查坐标是否在可见范围内
    const isVisible = scaledBounds.x >= 0 && scaledBounds.y >= 0 &&
                     scaledBounds.x < containerWidth && scaledBounds.y < containerHeight;

    // 输出前几个字符的详细坐标信息
    if (charIndex < 3 && elementIndex === 0) {
      console.log(`字符 "${char}" (${charIndex}) 坐标详情:`, {
        原始边界框: bounds,
        缩放后边界框: scaledBounds,
        缩放比例: scale,
        容器尺寸: { width: containerWidth, height: containerHeight },
        坐标检查: {
          x在范围内: scaledBounds.x >= 0 && scaledBounds.x < containerWidth,
          y在范围内: scaledBounds.y >= 0 && scaledBounds.y < containerHeight,
          右边界在范围内: (scaledBounds.x + scaledBounds.width) <= containerWidth,
          下边界在范围内: (scaledBounds.y + scaledBounds.height) <= containerHeight,
          完全可见: scaledBounds.x >= 0 && scaledBounds.y >= 0 &&
                   (scaledBounds.x + scaledBounds.width) <= containerWidth &&
                   (scaledBounds.y + scaledBounds.height) <= containerHeight
        }
      });
    }

    const color = getCharColor(charIndex, elementIndex);
    const uniqueKey = `char-${elementIndex}-${charIndex}`;

    return (
      <g key={uniqueKey}>
        {/* 边界框矩形 */}
        <rect
          x={scaledBounds.x}
          y={scaledBounds.y}
          width={scaledBounds.width}
          height={scaledBounds.height}
          fill="yellow" // 强制使用黄色填充，确保可见
          fillOpacity="0.5" // 固定透明度
          stroke="red" // 强制使用红色边框，确保可见
          strokeWidth="2" // 固定边框宽度
          strokeOpacity="1" // 完全不透明的边框
          strokeDasharray="none"
        />
        
        {/* 字符标签 */}
        {settings.showLabels && (
          <text
            x={scaledBounds.x + scaledBounds.width / 2}
            y={scaledBounds.y + scaledBounds.height / 2}
            textAnchor="middle"
            dominantBaseline="middle"
            fontSize={settings.labelSize}
            fill={color}
            fillOpacity={settings.opacity + 0.3}
            fontWeight="bold"
            style={{ pointerEvents: 'none' }}
          >
            {char}
          </text>
        )}
        
        {/* 字符索引 */}
        {settings.showLabels && (
          <text
            x={scaledBounds.x + 2}
            y={scaledBounds.y + settings.labelSize + 2}
            fontSize={Math.max(8, settings.labelSize - 2)}
            fill={color}
            fillOpacity={settings.opacity}
            style={{ pointerEvents: 'none' }}
          >
            {charIndex}
          </text>
        )}
      </g>
    );
  };

  // 渲染所有字符边界框
  const renderAllCharBounds = () => {
    const bounds = [];

    textElements.forEach((element, elementIndex) => {
      // 检查元素是否有char_pos数据
      if (!element.char_pos || !Array.isArray(element.char_pos)) {
        return;
      }

      const text = element.content || '';

      element.char_pos.forEach((charPos, charIndex) => {
        if (charIndex < text.length) {
          const char = text[charIndex];
          const charBound = renderCharBounds(charPos, charIndex, elementIndex, char, element);
          if (charBound) {
            bounds.push(charBound);
          }
        }
      });
    });

    return bounds;
  };

  // 更新设置
  const updateSetting = (key, value) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  // 切换显示状态
  const toggleVisibility = () => {
    const newVisibility = !settings.showBounds;
    updateSetting('showBounds', newVisibility);
    if (onVisibilityChange) {
      onVisibilityChange(newVisibility);
    }
  };

  useEffect(() => {
    updateSetting('showBounds', visible);
  }, [visible]);

  // 只显示SVG覆盖层
  if (!showSettingsPanel && showOverlay) {
    console.log('渲染SVG覆盖层 - 调试信息:', {
      showBounds: settings.showBounds,
      textElementsCount: textElements?.length || 0,
      containerSize: { width: containerWidth, height: containerHeight },
      hasCharPosData: textElements?.some(el => el.char_pos?.length > 0)
    });

    return (
      <svg
        width={containerWidth}
        height={containerHeight}
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          pointerEvents: 'none',
          zIndex: 1000,
          border: '2px solid lime' // 临时绿色边框，确保SVG可见
        }}
      >
        {/* 强制显示的测试矩形 - 无论设置如何 */}
        <rect
          x="50"
          y="50"
          width="200"
          height="100"
          fill="red"
          fillOpacity="0.5"
          stroke="red"
          strokeWidth="3"
        />
        <text
          x="150"
          y="100"
          textAnchor="middle"
          dominantBaseline="middle"
          fontSize="16"
          fill="white"
          fontWeight="bold"
        >
          测试边界框
        </text>

        {/* 强制可见的测试边界框 - 固定位置 */}
        <rect
          x="100"
          y="100"
          width="50"
          height="30"
          fill="lime"
          fillOpacity="0.7"
          stroke="purple"
          strokeWidth="3"
        />
        <text
          x="125"
          y="120"
          textAnchor="middle"
          dominantBaseline="middle"
          fontSize="12"
          fill="black"
          fontWeight="bold"
        >
          固定框
        </text>

        {/* 实际的字符边界框 */}
        {settings.showBounds && (() => {
          const charBounds = renderAllCharBounds();
          console.log('渲染的字符边界框数量:', charBounds?.length || 0);
          if (charBounds && charBounds.length > 0) {
            console.log('第一个字符边界框:', charBounds[0]);
          }
          return charBounds;
        })()}

        {/* 如果没有字符边界框，显示调试信息 */}
        {settings.showBounds && (!textElements || textElements.length === 0) && (
          <text
            x="50"
            y="200"
            fontSize="14"
            fill="orange"
            fontWeight="bold"
          >
            没有文本元素数据
          </text>
        )}

        {/* 显示文本元素数量 */}
        <text
          x="50"
          y="250"
          fontSize="12"
          fill="blue"
          fontWeight="bold"
        >
          文本元素: {textElements?.length || 0}
        </text>
      </svg>
    );
  }

  // 只显示设置面板
  if (showSettingsPanel && !showOverlay) {
    return (
      <Card
        size="small"
        title={
          <Space>
            <SettingOutlined />
            <Text>字符边界框设置</Text>
          </Space>
        }
        style={{ marginBottom: '16px' }}
      >
        <Row gutter={[16, 8]}>
          <Col span={6}>
            <Space>
              <Switch
                checked={settings.showBounds}
                onChange={toggleVisibility}
                checkedChildren={<EyeOutlined />}
                unCheckedChildren={<EyeInvisibleOutlined />}
              />
              <Text>显示边界框</Text>
            </Space>
          </Col>
          
          <Col span={6}>
            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              <Text>透明度</Text>
              <Slider
                min={0.1}
                max={1}
                step={0.1}
                value={settings.opacity}
                onChange={(value) => updateSetting('opacity', value)}
                disabled={!settings.showBounds}
              />
            </Space>
          </Col>
          
          <Col span={6}>
            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              <Text>线条宽度</Text>
              <Slider
                min={1}
                max={5}
                step={1}
                value={settings.strokeWidth}
                onChange={(value) => updateSetting('strokeWidth', value)}
                disabled={!settings.showBounds}
              />
            </Space>
          </Col>
          
          <Col span={6}>
            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              <Text>颜色模式</Text>
              <Select
                value={settings.colorMode}
                onChange={(value) => updateSetting('colorMode', value)}
                disabled={!settings.showBounds}
                style={{ width: '100%' }}
              >
                <Option value="rainbow">彩虹色</Option>
                <Option value="byElement">按元素</Option>
                <Option value="single">单一颜色</Option>
              </Select>
            </Space>
          </Col>
        </Row>
        
        <Row gutter={[16, 8]} style={{ marginTop: '8px' }}>
          <Col span={6}>
            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              <Text>线条样式</Text>
              <Select
                value={settings.strokeStyle}
                onChange={(value) => updateSetting('strokeStyle', value)}
                disabled={!settings.showBounds}
                style={{ width: '100%' }}
              >
                <Option value="solid">实线</Option>
                <Option value="dashed">虚线</Option>
              </Select>
            </Space>
          </Col>
          
          <Col span={6}>
            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              <Text>填充透明度</Text>
              <Slider
                min={0}
                max={0.5}
                step={0.05}
                value={settings.fillOpacity}
                onChange={(value) => updateSetting('fillOpacity', value)}
                disabled={!settings.showBounds}
              />
            </Space>
          </Col>
          
          <Col span={6}>
            <Space>
              <Switch
                checked={settings.showLabels}
                onChange={(checked) => updateSetting('showLabels', checked)}
                disabled={!settings.showBounds}
              />
              <Text>显示标签</Text>
            </Space>
          </Col>
          
          {settings.colorMode === 'single' && (
            <Col span={6}>
              <Space direction="vertical" size="small" style={{ width: '100%' }}>
                <Text>边框颜色</Text>
                <ColorPicker
                  value={settings.singleColor}
                  onChange={(color) => updateSetting('singleColor', color.toHexString())}
                  disabled={!settings.showBounds}
                />
              </Space>
            </Col>
          )}
        </Row>
      </Card>
    );
  }

  // 只显示SVG覆盖层
  if (!showSettingsPanel && showOverlay) {
    return (
      <div style={{ position: 'absolute', top: 0, left: 0, width: '100%', height: '100%', pointerEvents: 'none' }}>
        {settings.showBounds && (
          <svg
            width={containerWidth}
            height={containerHeight}
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              pointerEvents: 'none',
              zIndex: 10
            }}
          >
            {renderAllCharBounds()}
          </svg>
        )}
      </div>
    );
  }

  // 显示完整组件（设置面板 + SVG覆盖层）
  return (
    <div style={{ position: 'relative', width: '100%' }}>
      {/* 控制面板 */}
      <Card
        size="small"
        title={
          <Space>
            <SettingOutlined />
            <Text>字符边界框设置</Text>
          </Space>
        }
        style={{ marginBottom: '16px' }}
      >
        <Row gutter={[16, 8]}>
          <Col span={6}>
            <Space>
              <Switch
                checked={settings.showBounds}
                onChange={toggleVisibility}
                checkedChildren={<EyeOutlined />}
                unCheckedChildren={<EyeInvisibleOutlined />}
              />
              <Text>显示边界框</Text>
            </Space>
          </Col>

          <Col span={6}>
            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              <Text>颜色模式</Text>
              <Select
                value={settings.colorMode}
                onChange={(value) => updateSetting('colorMode', value)}
                disabled={!settings.showBounds}
                style={{ width: '100%' }}
              >
                <Option value="rainbow">彩虹色</Option>
                <Option value="byElement">按元素</Option>
                <Option value="single">单一颜色</Option>
              </Select>
            </Space>
          </Col>

          <Col span={6}>
            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              <Text>边框透明度</Text>
              <Slider
                min={0.1}
                max={1}
                step={0.1}
                value={settings.opacity}
                onChange={(value) => updateSetting('opacity', value)}
                disabled={!settings.showBounds}
              />
            </Space>
          </Col>

          <Col span={6}>
            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              <Text>线条宽度</Text>
              <Slider
                min={1}
                max={5}
                step={1}
                value={settings.strokeWidth}
                onChange={(value) => updateSetting('strokeWidth', value)}
                disabled={!settings.showBounds}
              />
            </Space>
          </Col>

          <Col span={6}>
            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              <Text>线条样式</Text>
              <Select
                value={settings.strokeStyle}
                onChange={(value) => updateSetting('strokeStyle', value)}
                disabled={!settings.showBounds}
                style={{ width: '100%' }}
              >
                <Option value="solid">实线</Option>
                <Option value="dashed">虚线</Option>
              </Select>
            </Space>
          </Col>

          <Col span={6}>
            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              <Text>填充透明度</Text>
              <Slider
                min={0}
                max={0.5}
                step={0.05}
                value={settings.fillOpacity}
                onChange={(value) => updateSetting('fillOpacity', value)}
                disabled={!settings.showBounds}
              />
            </Space>
          </Col>

          <Col span={6}>
            <Space>
              <Switch
                checked={settings.showLabels}
                onChange={(checked) => updateSetting('showLabels', checked)}
                disabled={!settings.showBounds}
              />
              <Text>显示标签</Text>
            </Space>
          </Col>

          {settings.colorMode === 'single' && (
            <Col span={6}>
              <Space direction="vertical" size="small" style={{ width: '100%' }}>
                <Text>边框颜色</Text>
                <ColorPicker
                  value={settings.singleColor}
                  onChange={(color) => updateSetting('singleColor', color.toHexString())}
                  disabled={!settings.showBounds}
                />
              </Space>
            </Col>
          )}
        </Row>
      </Card>

      {/* SVG 覆盖层 */}
      {settings.showBounds && (
        <div style={{ position: 'relative' }}>
          <svg
            width={containerWidth}
            height={containerHeight}
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              pointerEvents: 'none',
              zIndex: 10
            }}
          >
            {renderAllCharBounds()}
          </svg>
        </div>
      )}

      {/* 统计信息 */}
      {settings.showBounds && (
        <div style={{ marginTop: '8px' }}>
          <Text type="secondary">
            显示了 {textElements.reduce((total, element) =>
              total + (element.char_pos?.length || 0), 0
            )} 个字符边界框，
            来自 {textElements.filter(el => el.char_pos?.length > 0).length} 个文本元素
          </Text>
        </div>
      )}
    </div>
  );
};

export default CharBoundingBoxOverlay;
