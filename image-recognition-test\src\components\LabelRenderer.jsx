import React, { useState, useEffect, useRef } from 'react';
import { Card, Typography, Space, Tag, Divider, Input, message, Tooltip, Switch, Button } from 'antd';
import { QRCodeSVG } from 'qrcode.react';
import { EyeOutlined, EyeInvisibleOutlined } from '@ant-design/icons';
import BarcodeRenderer from './BarcodeRenderer';
import EditableText from './EditableText';
import CharBoundingBoxOverlay from './CharBoundingBoxOverlay';

const { Title, Text, Paragraph } = Typography;

// 元素类型映射
const ELEMENT_TYPE_NAMES = {
  '1': '文本',
  '2': '条形码',
  '3': '画布',
  '4': '线条',
  '5': 'Logo',
  '6': '图片',
  '7': '二维码',
  '8': '圆形',
  '9': '时间',
  '10': '表格',
  '11': '矩形'
};

const LabelRenderer = ({ data, onDataChange }) => {
  const [localData, setLocalData] = useState(data || []);
  const [containerWidth, setContainerWidth] = useState(800);
  const [showCharBounds, setShowCharBounds] = useState(false);
  const containerRef = useRef(null);

  // 将XPrinter的wordSpace毫米值转换为web显示的像素值
  const convertWordSpaceToPixels = (wordSpaceMm, scale) => {
    const wordSpaceValue = parseFloat(wordSpaceMm) || 0;
    if (wordSpaceValue <= 0) return 0;

    // 将毫米转换为像素，使用当前的缩放比例
    const wordSpacePx = wordSpaceValue * scale;

    // 确保最小值为0.1px，最大值为合理范围
    return Math.max(0, Math.min(wordSpacePx, 20));
  };

  useEffect(() => {
    setLocalData(data || []);
  }, [data]);

  // 监听条码解码完成事件
  useEffect(() => {
    const handleBarcodeDecoded = (event) => {
      const { base64, content, imageId, position } = event.detail;
      console.log('收到条码解码事件:', event.detail);

      // 使用 setTimeout 避免在渲染过程中更新状态
      setTimeout(() => {
        setLocalData(prevData => {
          const newData = prevData.map(element => {
            // 精确匹配条码元素
            if ((element.type === '2' || element.type === '7') &&
                element.content &&
                element.content.includes('识别中')) {

              // 通过多种方式匹配同一个条码
              let isMatch = false;

              // 方法1: 通过 imageId 匹配
              if (imageId && element.imageId === imageId) {
                isMatch = true;
              }

              // 方法2: 通过 base64 数据匹配
              if (!isMatch && base64 && element.base64Data === base64) {
                isMatch = true;
              }

              // 方法3: 通过位置匹配（比较位置数组）
              if (!isMatch && position && element.originalPosition) {
                const posMatch = JSON.stringify(position) === JSON.stringify(element.originalPosition);
                if (posMatch) {
                  isMatch = true;
                }
              }

              if (isMatch) {
                console.log('精确匹配并更新条码内容:', element.content, '->', content);
                return { ...element, content: content };
              }
            }
            return element;
          });

          // 通知父组件数据变化
          if (onDataChange) {
            onDataChange(newData);
          }

          return newData;
        });
      }, 0);
    };

    window.addEventListener('barcodeDecoded', handleBarcodeDecoded);

    return () => {
      window.removeEventListener('barcodeDecoded', handleBarcodeDecoded);
    };
  }, [onDataChange]);

  useEffect(() => {
    const resizeObserver = new ResizeObserver(entries => {
      if (entries[0]) {
        setContainerWidth(entries[0].contentRect.width);
      }
    });

    const currentRef = containerRef.current;
    if (currentRef) {
      resizeObserver.observe(currentRef);
    }

    return () => {
      if (currentRef) {
        resizeObserver.unobserve(currentRef);
      }
    };
  }, []);

  // 更新元素内容
  const updateElementContent = (index, newContent) => {
    const newData = [...localData];
    newData[index] = { ...newData[index], content: newContent };
    setLocalData(newData);

    // 通知父组件数据变化
    if (onDataChange) {
      onDataChange(newData);
    }
  };

  if (!localData || !Array.isArray(localData) || localData.length === 0) {
    return (
      <Card title="标签预览">
        <Text type="secondary">暂无数据</Text>
      </Card>
    );
  }

  // 查找画布元素
  const canvasElement = localData.find(item => item.type === '3' || item.elementType === '3');
  const canvasWidthMm = canvasElement ? parseFloat(canvasElement.width) || 40 : 40;
  const canvasHeightMm = canvasElement ? parseFloat(canvasElement.height) || 30 : 30;

  // 获取原始图像尺寸信息
  const originalImageWidthPx = canvasElement?.originalImageWidthPx || 0;
  const originalImageHeightPx = canvasElement?.originalImageHeightPx || 0;

  // 计算缩放比例，将mm单位的画布渲染到合适的像素尺寸
  const maxDisplayWidthPx = containerWidth > 0 ? containerWidth : 800; // 使用动态宽度
  const scale = maxDisplayWidthPx / canvasWidthMm; // px/mm

  const displayWidth = canvasWidthMm * scale;
  const displayHeight = canvasHeightMm * scale;

  // 过滤掉画布元素，只渲染其他元素
  const renderElements = localData.filter(item => item.type !== '3' && item.elementType !== '3');

  const renderElement = (element, renderIndex) => {
    // 找到元素在原始数据中的真实索引
    const actualIndex = localData.findIndex(item => item === element);
    const x = (parseFloat(element.x) || 0) * scale;
    const y = (parseFloat(element.y) || 0) * scale;
    const width = (parseFloat(element.width) || 10) * scale;
    const height = (parseFloat(element.height) || 5) * scale;
    const rotation = parseInt(element.rotational) || 0;

    const baseStyle = {
      position: 'absolute',
      left: `${x}px`,
      top: `${y}px`,
      width: `${width}px`,
      height: `${height}px`,
      transform: rotation !== 0 ? `rotate(${rotation}deg)` : 'none',
      transformOrigin: 'top left',
      border: '1px dashed #ccc',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      fontSize: `${Math.max(10, height * 0.6)}px`,
      overflow: 'hidden'
    };

    switch (element.type) {
      case '1': // 文本
        {
          // 计算字间距
          const letterSpacing = convertWordSpaceToPixels(element.wordSpace, scale);

          return (
            <div
              key={renderIndex}
              style={{
                ...baseStyle,
                backgroundColor: 'rgba(24, 144, 255, 0.1)',
                borderColor: '#1890ff',
                padding: '2px',
                textAlign: element.hAlignment === '2' ? 'center' :
                  element.hAlignment === '3' ? 'right' : 'left',
                fontWeight: element.bold === 'true' ? 'bold' : 'normal',
                fontStyle: element.italic === 'true' ? 'italic' : 'normal',
                textDecoration: element.underline === 'true' ? 'underline' : 'none',
                letterSpacing: `${letterSpacing}px`
              }}
              title={`文本: ${element.content || '空文本'} (字间距: ${element.wordSpace || 0}mm) (点击编辑)`}
            >
              <EditableText
                value={element.content || ''}
                onChange={(newContent) => updateElementContent(actualIndex, newContent)}
                placeholder="点击编辑文本"
                stretch={true}
                style={{
                  fontSize: 'inherit',
                  fontWeight: 'inherit',
                  fontStyle: 'inherit',
                  textDecoration: 'inherit',
                  textAlign: 'inherit',
                  letterSpacing: 'inherit'
                }}
                textStyle={{
                  fontSize: 'inherit',
                  lineHeight: '1.2',
                  wordBreak: 'break-all',
                  letterSpacing: 'inherit'
                }}
              />
            </div>
          );
        }

      case '2': // 条形码
        return (
          <div
            key={renderIndex}
            style={{
              ...baseStyle,
              backgroundColor: 'rgba(82, 196, 26, 0.1)',
              borderColor: '#52c41a',
              padding: '4px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
            title={`条形码: ${element.content || '123456789'} (点击编辑)`}
          >
            <div style={{ width: '100%', height: '100%', position: 'relative' }}>
              <BarcodeRenderer
                value={element.content || '123456789'}
                format={element.barcodeType || 'CODE128'}
                width={Math.max(1, Math.floor(width / 100))}
                height={Math.max(20, height - 20)}
                displayValue={element.showText !== 'false'}
                fontSize={Math.max(8, Math.floor(height / 8))}
                textAlign={element.textAlignment === '1' ? 'left' :
                  element.textAlignment === '3' ? 'right' : 'center'}
                style={{
                  width: '100%',
                  height: '100%'
                }}
              />
              <div
                style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  cursor: 'pointer',
                  background: 'transparent'
                }}
                onClick={() => {
                  const newContent = prompt('请输入条码内容:', element.content || '123456789');
                  if (newContent !== null && newContent !== element.content) {
                    updateElementContent(index, newContent);
                  }
                }}
              />
            </div>
          </div>
        );

      case '7': // 二维码
        return (
          <div
            key={renderIndex}
            style={{
              ...baseStyle,
              backgroundColor: 'rgba(250, 173, 20, 0.1)',
              borderColor: '#faad14',
              padding: '4px',
              position: 'relative'
            }}
            title={`二维码: ${element.content || 'QR Code'} (点击编辑)`}
          >
            <div style={{
              width: '100%',
              height: '100%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              {width > 30 && height > 30 ? (
                <QRCodeSVG
                  value={element.content || 'QR Code'}
                  size={Math.min(width - 8, height - 8)}
                  level="M"
                />
              ) : (
                <div style={{
                  width: '80%',
                  height: '80%',
                  backgroundColor: '#000',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: 'white',
                  fontSize: '6px'
                }}>
                  QR
                </div>
              )}
            </div>
            <div
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                cursor: 'pointer',
                background: 'transparent'
              }}
              onClick={() => {
                const newContent = prompt('请输入二维码内容:', element.content || 'QR Code');
                if (newContent !== null && newContent !== element.content) {
                  updateElementContent(index, newContent);
                }
              }}
            />
          </div>
        );

      case '6': // 图片
        return (
          <div
            key={renderIndex}
            style={{
              ...baseStyle,
              backgroundColor: 'rgba(114, 46, 209, 0.1)',
              borderColor: '#722ed1'
            }}
            title="图片"
          >
            {element.content && element.content.startsWith('data:image') ? (
              <img
                src={element.content}
                alt="识别的图片"
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'contain'
                }}
              />
            ) : (
              <Text style={{ fontSize: 'inherit' }}>图片</Text>
            )}
          </div>
        );

      case '10': // 表格
        return (
          <div
            key={renderIndex}
            style={{
              ...baseStyle,
              backgroundColor: 'rgba(235, 47, 6, 0.1)',
              borderColor: '#eb2f06',
              padding: '2px'
            }}
            title="表格"
          >
            {element.cells && Array.isArray(element.cells) ? (
              <div style={{
                width: '100%',
                height: '100%',
                display: 'grid',
                gridTemplateColumns: element.columnWidths && element.columnWidths.length > 0
                  ? element.columnWidths.map(w => `${w}fr`).join(' ')
                  : 'repeat(auto-fit, minmax(20px, 1fr))',
                gridTemplateRows: element.rowHeights && element.rowHeights.length > 0
                  ? element.rowHeights.map(h => `${h}fr`).join(' ')
                  : 'repeat(auto-fit, minmax(10px, 1fr))',
                gap: '1px',
                backgroundColor: '#ccc'
              }}>
                {element.cells.map((cell, cellIndex) => {
                  // 解析单元格内容的 markdown 样式
                  const cellContent = cell.content || '';
                  const isBold = cellContent.startsWith('**') && cellContent.endsWith('**');
                  const displayContent = isBold ? cellContent.slice(2, -2) : cellContent;

                  // 直接使用数据转换时计算的像素字体大小
                  const fontSize = parseFloat(cell.textSize) || 12;

                  // 计算表格单元格的字间距
                  const cellLetterSpacing = convertWordSpaceToPixels(cell.wordSpace, scale);

                  return (
                    <div
                      key={cellIndex}
                      style={{
                        backgroundColor: 'white',
                        fontSize: fontSize + 'px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center', // 默认居中对齐
                        padding: '2px 4px',
                        gridColumn: `${parseInt(cell.col) + 1} / span ${parseInt(cell.colSpan) || 1}`,
                        gridRow: `${parseInt(cell.row) + 1} / span ${parseInt(cell.rowSpan) || 1}`,
                        fontWeight: (cell.bold === 'true' || isBold) ? 'bold' : 'normal',
                        fontStyle: cell.italic === 'true' ? 'italic' : 'normal',
                        textDecoration: cell.underline === 'true' ? 'underline' : 'none',
                        letterSpacing: `${cellLetterSpacing}px`,
                        overflow: 'hidden',
                        cursor: 'pointer',
                        border: '1px solid #e8e8e8',
                        position: 'relative',
                        minHeight: '20px'
                      }}
                      title={`单元格 (${cell.row}, ${cell.col}): ${displayContent || '空'} (点击编辑)`}
                      onClick={() => {
                        const newContent = prompt(
                          `编辑单元格 (${parseInt(cell.row) + 1}, ${parseInt(cell.col) + 1}) 内容:`,
                          displayContent
                        );
                        if (newContent !== null && newContent !== displayContent) {
                          // 保持原有的 markdown 格式
                          const finalContent = isBold ? `**${newContent}**` : newContent;

                          // 更新单元格内容
                          const newData = [...localData];
                          const tableIndex = localData.findIndex(item => item === element);
                          if (tableIndex !== -1) {
                            const newCells = [...newData[tableIndex].cells];
                            newCells[cellIndex] = { ...newCells[cellIndex], content: finalContent };
                            newData[tableIndex] = { ...newData[tableIndex], cells: newCells };
                            setLocalData(newData);

                            // 通知父组件数据变化
                            if (onDataChange) {
                              onDataChange(newData);
                            }
                          }
                        }
                      }}
                    >
                      <EditableText
                        value={displayContent}
                        onChange={(newContent) => {
                          // 保持原有的 markdown 格式
                          const finalContent = isBold ? `**${newContent}**` : newContent;

                          // 更新单元格内容
                          const newData = [...localData];
                          const tableIndex = localData.findIndex(item => item === element);
                          if (tableIndex !== -1) {
                            const newCells = [...newData[tableIndex].cells];
                            newCells[cellIndex] = { ...newCells[cellIndex], content: finalContent };
                            newData[tableIndex] = { ...newData[tableIndex], cells: newCells };
                            setLocalData(newData);

                            // 通知父组件数据变化
                            if (onDataChange) {
                              onDataChange(newData);
                            }
                          }
                        }}
                        placeholder="点击编辑"
                        stretch={false}
                        style={{
                          fontSize: 'inherit',
                          fontWeight: 'inherit',
                          fontStyle: 'inherit',
                          textDecoration: 'inherit',
                          letterSpacing: 'inherit',
                          textAlign: 'center', // 默认居中对齐
                          width: '100%',
                          minHeight: '16px',
                          lineHeight: '1.2',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center'
                        }}
                        textStyle={{
                          fontSize: 'inherit',
                          lineHeight: '1.2',
                          textAlign: 'center',
                          letterSpacing: 'inherit',
                          wordBreak: cell.lineWrap === 'true' ? 'break-word' : 'keep-all',
                          whiteSpace: cell.lineWrap === 'true' ? 'normal' : 'nowrap'
                        }}
                      />
                    </div>
                  );
                })}
              </div>
            ) : (
              <div style={{
                width: '100%',
                height: '100%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '8px',
                color: '#999'
              }}>
                表格数据缺失
              </div>
            )}
          </div>
        );

      default:
        return (
          <div
            key={renderIndex}
            style={{
              ...baseStyle,
              backgroundColor: 'rgba(140, 140, 140, 0.1)',
              borderColor: '#8c8c8c'
            }}
            title={`${ELEMENT_TYPE_NAMES[element.type] || '未知类型'}`}
          >
            <Text style={{ fontSize: 'inherit' }}>
              {ELEMENT_TYPE_NAMES[element.type] || '未知'}
            </Text>
          </div>
        );
    }
  };

  return (
    <div style={{ position: 'relative' }}>
        <Card
        title="标签预览"
        extra={
          <Space>
            <div>
              <Text strong>画布尺寸: </Text>
              <Text code>{canvasWidthMm} mm × {canvasHeightMm} mm</Text>
              <Text type="secondary" style={{ marginLeft: 16 }}>
                显示比例: 1mm ≈ {scale.toFixed(2)}px
              </Text>
            </div>
            <Button
              type={showCharBounds ? "primary" : "default"}
              size="small"
              icon={showCharBounds ? <EyeOutlined /> : <EyeInvisibleOutlined />}
              onClick={() => setShowCharBounds(!showCharBounds)}
            >
              字符边框
            </Button>
          </Space>
        }
      >
        <div ref={containerRef}>
        {/* 字符边界框设置面板 - 放在画布上方 */}
        {showCharBounds && (
          <div style={{ marginBottom: '16px' }}>
            <CharBoundingBoxOverlay
              textElements={renderElements.filter(el => el.type === '1' && el.char_pos)}
              scale={scale}
              containerWidth={displayWidth}
              containerHeight={displayHeight}
              visible={showCharBounds}
              onVisibilityChange={setShowCharBounds}
              showSettingsPanel={true}
              showOverlay={false}
            />
          </div>
        )}

        {renderElements.length > 0 ? (
          <div style={{ position: 'relative' }}>
            {/* 画布容器 */}
            <div
              style={{
                position: 'relative',
                width: `${displayWidth}px`,
                height: `${displayHeight}px`,
                backgroundColor: '#f9f9f9',
                border: '1px solid #e8e8e8',
                boxSizing: 'content-box',
                overflow: 'visible' // 改为visible，避免裁剪SVG
              }}
            >
              {renderElements.map(renderElement)}
            </div>

            {/* 字符边界框SVG覆盖层 - 放在画布外层 */}
            {showCharBounds && (() => {
              const textElementsWithCharPos = renderElements.filter(el => el.type === '1' && el.char_pos);
              console.log('LabelRenderer - 字符边界框数据:', {
                showCharBounds,
                totalElements: renderElements.length,
                textElements: renderElements.filter(el => el.type === '1').length,
                textElementsWithCharPos: textElementsWithCharPos.length,
                scale,
                displaySize: { width: displayWidth, height: displayHeight },
                sampleElement: textElementsWithCharPos[0] ? {
                  type: textElementsWithCharPos[0].type,
                  content: textElementsWithCharPos[0].content?.substring(0, 10),
                  hasCharPos: !!textElementsWithCharPos[0].char_pos,
                  charPosLength: textElementsWithCharPos[0].char_pos?.length,
                  charPosSample: textElementsWithCharPos[0].char_pos?.slice(0, 2)
                } : null,
                allTextElements: textElementsWithCharPos.map(el => ({
                  content: el.content?.substring(0, 10),
                  charPosLength: el.char_pos?.length
                }))
              });

              return (
                <div style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: `${displayWidth}px`,
                  height: `${displayHeight}px`,
                  pointerEvents: 'none',
                  zIndex: 1100 // 比Image预览的1080高
                }}>
                  <CharBoundingBoxOverlay
                    textElements={textElementsWithCharPos}
                    scale={scale}
                    containerWidth={displayWidth}
                    containerHeight={displayHeight}
                    canvasWidthMm={canvasWidthMm}
                    canvasHeightMm={canvasHeightMm}
                    originalImageWidthPx={originalImageWidthPx}
                    originalImageHeightPx={originalImageHeightPx}
                    visible={showCharBounds}
                    onVisibilityChange={setShowCharBounds}
                    showSettingsPanel={false}
                    showOverlay={true}
                  />
                </div>
              );
            })()}
          </div>
        ) : (
          <div style={{
            textAlign: 'center',
            padding: '60px 20px',
            color: '#999'
          }}>
            <Paragraph>
              暂无预览元素
            </Paragraph>
          </div>
        )}
      </div>
      <Divider />
      <Title level={5}>元素数据</Title>
      <div style={{ maxHeight: 400, overflowY: 'auto', padding: '0 8px' }}>
        {localData.map((element, index) => (
          <Card key={index} size="small" style={{ marginBottom: 12 }}>
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <div>
                <Tag color="blue">
                  {ELEMENT_TYPE_NAMES[element.elementType || element.type] || '未知'}
                </Tag>
                <Text>
                  位置: ({element.x} mm, {element.y} mm)
                </Text>
                <Text style={{ marginLeft: 8 }}>
                  尺寸: {element.width} mm × {element.height} mm
                </Text>
                {element.content && (
                  <Tooltip title={element.content}>
                    <Paragraph
                      ellipsis={{ rows: 1 }}
                      style={{ maxWidth: 300, margin: '8px 0 0' }}
                    >
                      内容: <Text type="secondary">{element.content}</Text>
                    </Paragraph>
                  </Tooltip>
                )}
              </div>
            </div>
          </Card>
        ))}
        </div>
      </Card>

      {/* 字符边界框覆盖层 - 使用相对定位，但在Card外层 */}
      {showCharBounds && (() => {
        const textElementsWithCharPos = renderElements.filter(el => el.type === '1' && el.char_pos);

        return (
          <div style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: `${displayWidth}px`,
            height: `${displayHeight}px`,
            pointerEvents: 'none',
            zIndex: 10000 // 先用一个合理的z-index值测试
          }}>
            <CharBoundingBoxOverlay
              textElements={textElementsWithCharPos}
              scale={scale}
              containerWidth={displayWidth}
              containerHeight={displayHeight}
              canvasWidthMm={canvasWidthMm}
              canvasHeightMm={canvasHeightMm}
              originalImageWidthPx={originalImageWidthPx}
              originalImageHeightPx={originalImageHeightPx}
              visible={showCharBounds}
              onVisibilityChange={setShowCharBounds}
              showSettingsPanel={false}
              showOverlay={true}
            />
          </div>
        );
      })()}
    </div>
  );
};

export default LabelRenderer;
